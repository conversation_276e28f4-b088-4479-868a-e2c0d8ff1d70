#!/usr/bin/env python3
"""
Remote MCP Client for testing the remote MCP server
"""

import asyncio
import json
import uuid
import aiohttp
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RemoteMCPClient:
    """Client for connecting to remote MCP server via HTTP/SSE."""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.session_id = str(uuid.uuid4())
        self.request_id = 0
        
    def get_next_id(self):
        """Get next request ID."""
        self.request_id += 1
        return self.request_id
    
    async def send_request(self, method: str, params: dict = None) -> dict:
        """Send MCP request to remote server."""
        if params is None:
            params = {}
            
        request = {
            "jsonrpc": "2.0",
            "id": self.get_next_id(),
            "method": method,
            "params": params
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-Session-ID": self.session_id
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/mcp",
                json=request,
                headers=headers
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")
    
    async def initialize(self):
        """Initialize the MCP session."""
        return await self.send_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "remote-test-client",
                "version": "1.0.0"
            }
        })
    
    async def list_tools(self):
        """List available tools."""
        return await self.send_request("tools/list")
    
    async def call_tool(self, name: str, arguments: dict):
        """Call a tool."""
        return await self.send_request("tools/call", {
            "name": name,
            "arguments": arguments
        })
    
    async def test_connection(self):
        """Test the connection to remote server."""
        try:
            # Test health endpoint
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/health") as response:
                    if response.status == 200:
                        health_data = await response.json()
                        print(f"✅ Server health: {health_data}")
                        return True
                    else:
                        print(f"❌ Health check failed: {response.status}")
                        return False
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False

class WebSocketMCPClient:
    """Client for connecting to remote MCP server via WebSocket."""
    
    def __init__(self, ws_url: str = "ws://localhost:8080/ws"):
        self.ws_url = ws_url
        self.request_id = 0
        self.ws = None
        
    def get_next_id(self):
        """Get next request ID."""
        self.request_id += 1
        return self.request_id
    
    async def connect(self):
        """Connect to WebSocket server."""
        session = aiohttp.ClientSession()
        self.ws = await session.ws_connect(self.ws_url)
        
        # Wait for welcome message
        msg = await self.ws.receive()
        if msg.type == aiohttp.WSMsgType.TEXT:
            welcome = json.loads(msg.data)
            print(f"📡 WebSocket connected: {welcome}")
        
        return session
    
    async def send_request(self, method: str, params: dict = None) -> dict:
        """Send MCP request via WebSocket."""
        if not self.ws:
            raise Exception("Not connected to WebSocket")
            
        if params is None:
            params = {}
            
        request = {
            "jsonrpc": "2.0",
            "id": self.get_next_id(),
            "method": method,
            "params": params
        }
        
        await self.ws.send_str(json.dumps(request))
        
        # Wait for response
        msg = await self.ws.receive()
        if msg.type == aiohttp.WSMsgType.TEXT:
            return json.loads(msg.data)
        else:
            raise Exception(f"Unexpected message type: {msg.type}")
    
    async def close(self, session):
        """Close WebSocket connection."""
        if self.ws:
            await self.ws.close()
        await session.close()

async def test_http_client():
    """Test HTTP/SSE client."""
    print("🧪 Testing HTTP/SSE Remote MCP Client")
    print("=" * 50)
    
    client = RemoteMCPClient()
    
    # Test connection
    if not await client.test_connection():
        print("❌ Cannot connect to server. Make sure remote_server.py is running.")
        return
    
    try:
        # Initialize
        print("\n🚀 Initializing MCP session...")
        init_response = await client.initialize()
        print(f"✅ Initialized: {init_response['result']['serverInfo']}")
        
        # List tools
        print("\n📋 Listing available tools...")
        tools_response = await client.list_tools()
        tools = tools_response['result']['tools']
        print(f"✅ Found {len(tools)} tools:")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # Call tool
        print("\n🔍 Testing search_datasource_info tool...")
        tool_response = await client.call_tool("search_datasource_info", {
            "query": "test",
            "page": 1,
            "size": 5
        })
        
        print("✅ Tool call successful!")
        content = tool_response['result']['content'][0]['text']
        result = json.loads(content)
        print(f"📄 Response: {result['status']} (server_type: {result.get('server_type', 'unknown')})")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

async def test_websocket_client():
    """Test WebSocket client."""
    print("\n🧪 Testing WebSocket Remote MCP Client")
    print("=" * 50)
    
    client = WebSocketMCPClient()
    
    try:
        # Connect
        print("📡 Connecting to WebSocket...")
        session = await client.connect()
        
        # Initialize
        print("🚀 Initializing MCP session...")
        init_response = await client.send_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "websocket-test-client",
                "version": "1.0.0"
            }
        })
        print(f"✅ Initialized: {init_response['result']['serverInfo']}")
        
        # List tools
        print("📋 Listing available tools...")
        tools_response = await client.send_request("tools/list")
        tools = tools_response['result']['tools']
        print(f"✅ Found {len(tools)} tools:")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # Call tool
        print("🔍 Testing search_datasource_info tool...")
        tool_response = await client.send_request("tools/call", {
            "name": "search_datasource_info",
            "arguments": {
                "query": "websocket-test",
                "page": 1,
                "size": 3
            }
        })
        
        print("✅ Tool call successful!")
        content = tool_response['result']['content'][0]['text']
        result = json.loads(content)
        print(f"📄 Response: {result['status']} (server_type: {result.get('server_type', 'unknown')})")
        
        # Close connection
        await client.close(session)
        print("👋 WebSocket connection closed")
        
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")

async def main():
    """Main test function."""
    print("🌐 Remote MCP Client Test Suite")
    print("=" * 60)
    
    # Test HTTP client
    await test_http_client()
    
    # Wait a bit
    await asyncio.sleep(1)
    
    # Test WebSocket client
    await test_websocket_client()
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
