# Remote MCP Server 部署和使用指南

## 概述

Remote MCP Server 允许你将MCP服务部署到远程服务器，客户端通过网络协议（HTTP/SSE或WebSocket）连接，而不需要本地进程。

## 🌐 Remote vs Local MCP Server

### Local MCP Server
- ✅ 通过stdin/stdout通信
- ✅ 本地进程启动
- ❌ 单客户端连接
- ❌ 需要本地部署

### Remote MCP Server  
- ✅ 通过HTTP/WebSocket通信
- ✅ 网络访问
- ✅ 多客户端并发
- ✅ 可部署到云服务器
- ✅ 更好的扩展性

## 🚀 快速开始

### 1. 启动Remote Server

```bash
# 本地开发
python3 remote_server.py

# 或使用Docker
docker-compose up -d
```

### 2. 测试连接

```bash
# 测试客户端
python3 remote_client.py

# 或直接访问
curl http://localhost:8080/health
```

### 3. 在MCP客户端中配置

```json
{
  "mcpServers": {
    "remote-datasource": {
      "transport": {
        "type": "sse",
        "url": "http://localhost:8080/mcp"
      }
    }
  }
}
```

## 📡 支持的传输协议

### 1. Server-Sent Events (SSE)
- **URL**: `http://localhost:8080/mcp`
- **特点**: HTTP长连接，服务器推送
- **适用**: 大多数MCP客户端

### 2. WebSocket
- **URL**: `ws://localhost:8080/ws`
- **特点**: 全双工通信，实时性好
- **适用**: 需要高实时性的场景

### 3. HTTP轮询
- **URL**: `http://localhost:8080/mcp`
- **特点**: 简单但效率较低
- **适用**: 简单的集成场景

## 🔧 部署选项

### 本地开发
```bash
python3 remote_server.py
```

### Docker部署
```bash
# 构建镜像
docker build -t remote-mcp-server .

# 运行容器
docker run -p 8080:8080 remote-mcp-server
```

### Docker Compose
```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 云服务器部署

#### 1. 准备服务器
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 2. 部署应用
```bash
# 克隆代码
git clone <your-repo>
cd remote_mcp_test

# 启动服务
docker-compose up -d

# 配置防火墙
sudo ufw allow 8080
```

#### 3. 配置域名和SSL
```bash
# 使用Nginx反向代理
# 配置SSL证书（Let's Encrypt）
sudo certbot --nginx -d your-domain.com
```

## 🔐 安全配置

### 1. 添加认证
```python
# 在remote_server.py中添加
@web.middleware
async def auth_middleware(request, handler):
    token = request.headers.get('Authorization')
    if not token or not validate_token(token):
        return web.json_response({'error': 'Unauthorized'}, status=401)
    return await handler(request)
```

### 2. HTTPS配置
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location /mcp {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. 防火墙配置
```bash
# 只允许必要端口
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

## 📋 客户端配置示例

### Claude Desktop
```json
{
  "mcpServers": {
    "remote-datasource": {
      "transport": {
        "type": "sse",
        "url": "https://your-domain.com/mcp",
        "headers": {
          "Authorization": "Bearer your-token"
        }
      }
    }
  }
}
```

### 自定义客户端
```python
import aiohttp
import json

async def connect_to_remote_mcp():
    session_id = "unique-session-id"
    
    # 建立SSE连接
    async with aiohttp.ClientSession() as session:
        async with session.get(
            "https://your-domain.com/mcp",
            params={"session_id": session_id}
        ) as sse_response:
            # 处理SSE消息
            async for line in sse_response.content:
                if line.startswith(b"data: "):
                    data = json.loads(line[6:])
                    print(f"Received: {data}")
        
        # 发送MCP请求
        await session.post(
            "https://your-domain.com/mcp",
            json={
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/list"
            },
            headers={"X-Session-ID": session_id}
        )
```

## 🔍 监控和调试

### 健康检查
```bash
curl http://localhost:8080/health
```

### 查看活跃会话
```bash
curl http://localhost:8080/sessions
```

### 日志监控
```bash
# Docker日志
docker-compose logs -f remote-mcp-server

# 系统日志
journalctl -u your-service -f
```

### 性能监控
```bash
# 安装监控工具
pip install prometheus-client

# 添加metrics端点
@app.route('/metrics')
async def metrics():
    return web.Response(text=generate_latest())
```

## 🚨 故障排除

### 常见问题

1. **连接被拒绝**
   ```bash
   # 检查服务状态
   docker-compose ps
   
   # 检查端口
   netstat -tlnp | grep 8080
   ```

2. **CORS错误**
   ```python
   # 确保CORS中间件正确配置
   response.headers['Access-Control-Allow-Origin'] = '*'
   ```

3. **SSL证书问题**
   ```bash
   # 检查证书有效性
   openssl x509 -in cert.pem -text -noout
   ```

### 调试模式
```bash
# 启用调试日志
export LOG_LEVEL=DEBUG
python3 remote_server.py
```

## 📈 扩展和优化

### 负载均衡
```yaml
# docker-compose.yml
services:
  remote-mcp-server:
    deploy:
      replicas: 3
  
  nginx:
    # 配置负载均衡
```

### 缓存优化
```python
# 添加Redis缓存
import redis
cache = redis.Redis(host='localhost', port=6379)
```

### 数据库持久化
```python
# 添加数据库支持
import asyncpg
async def init_db():
    return await asyncpg.connect("postgresql://...")
```

## 🎯 最佳实践

1. **使用HTTPS**: 生产环境必须使用SSL
2. **添加认证**: 保护API访问
3. **监控日志**: 及时发现问题
4. **备份数据**: 定期备份重要数据
5. **更新依赖**: 保持依赖包最新
6. **限流保护**: 防止API滥用
7. **健康检查**: 配置自动重启

## 📚 相关资源

- [MCP协议规范](https://modelcontextprotocol.io)
- [aiohttp文档](https://docs.aiohttp.org)
- [Docker部署指南](https://docs.docker.com)
- [Nginx配置](https://nginx.org/en/docs)

通过这个Remote MCP Server，你可以将数据源查询服务部署到任何支持HTTP的环境中，为多个客户端提供服务！
