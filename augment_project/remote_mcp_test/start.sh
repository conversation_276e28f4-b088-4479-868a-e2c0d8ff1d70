#!/bin/bash

# Datasource Query Server Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if Python 3 is available
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed or not in PATH"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_status "Using Python $PYTHON_VERSION"
}

# Install dependencies
install_deps() {
    print_status "Installing dependencies..."
    if [ -f "requirements.txt" ]; then
        python3 -m pip install -r requirements.txt --user
        print_status "Dependencies installed successfully"
    else
        print_warning "requirements.txt not found, installing aiohttp directly"
        python3 -m pip install aiohttp --user
    fi
}

# Show usage
show_usage() {
    print_header "Datasource Query Server"
    echo ""
    echo "Usage: $0 [mode]"
    echo ""
    echo "Modes:"
    echo "  mcp     - Start local MCP server (default)"
    echo "  remote  - Start remote MCP server"
    echo "  http    - Start HTTP API server"
    echo "  test    - Test local MCP server"
    echo "  test-remote - Test remote MCP server"
    echo "  install - Install dependencies only"
    echo "  help    - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 mcp         # Start local MCP server"
    echo "  $0 remote      # Start remote MCP server on port 8080"
    echo "  $0 http        # Start HTTP API server on port 8080"
    echo "  $0 test        # Test the local MCP server"
    echo "  $0 test-remote # Test the remote MCP server"
}

# Start MCP server
start_mcp() {
    print_header "Starting MCP Server..."
    print_status "MCP server will communicate via stdin/stdout"
    print_status "Press Ctrl+C to stop"
    echo ""
    python3 server.py
}

# Start remote MCP server
start_remote() {
    print_header "Starting Remote MCP Server..."
    print_status "Remote MCP server will start on http://localhost:8080"
    print_status "Supports SSE and WebSocket connections"
    print_status "Press Ctrl+C to stop"
    echo ""
    python3 remote_server.py
}

# Start HTTP server
start_http() {
    print_header "Starting HTTP API Server..."
    print_status "HTTP API server will start on http://localhost:8080"
    print_status "Press Ctrl+C to stop"
    echo ""
    python3 http_server.py
}

# Test local MCP server
test_mcp() {
    print_header "Testing Local MCP Server..."
    python3 test_client.py
}

# Test remote MCP server
test_remote() {
    print_header "Testing Remote MCP Server..."
    python3 remote_client.py
}

# Main script
main() {
    check_python
    
    MODE=${1:-mcp}
    
    case $MODE in
        "mcp")
            install_deps
            start_mcp
            ;;
        "remote")
            install_deps
            start_remote
            ;;
        "http")
            install_deps
            start_http
            ;;
        "test")
            install_deps
            test_mcp
            ;;
        "test-remote")
            install_deps
            test_remote
            ;;
        "install")
            install_deps
            print_status "Dependencies installed. You can now run the server."
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown mode: $MODE"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
