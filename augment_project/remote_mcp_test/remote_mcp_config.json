{"mcpServers": {"remote-datasource-query": {"transport": {"type": "sse", "url": "http://localhost:8080/mcp"}, "description": "Remote MCP Server for data source queries", "capabilities": ["tools"]}}, "alternativeConfigs": {"websocket": {"remote-datasource-query-ws": {"transport": {"type": "websocket", "url": "ws://localhost:8080/ws"}, "description": "Remote MCP Server via WebSocket", "capabilities": ["tools"]}}, "production": {"remote-datasource-query-prod": {"transport": {"type": "sse", "url": "https://your-domain.com/mcp"}, "description": "Production Remote MCP Server", "capabilities": ["tools"], "auth": {"type": "bearer", "token": "your-api-token"}}}}}