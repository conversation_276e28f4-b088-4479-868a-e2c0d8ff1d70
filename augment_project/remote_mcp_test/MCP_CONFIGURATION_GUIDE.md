# MCP Server 配置指南

## 概述

本指南将帮助你在各种MCP客户端中配置和使用Datasource Query MCP Server。

## 配置文件说明

### 基本配置 (mcp_config.json)

```json
{
  "mcpServers": {
    "datasource-query": {
      "command": "python3",
      "args": ["server.py"],
      "cwd": "/Users/<USER>/augment_project/remote_mcp_test",
      "env": {
        "PYTHONPATH": "/Users/<USER>/augment_project/remote_mcp_test"
      }
    }
  }
}
```

### 配置参数说明

- **`datasource-query`**: 服务器名称，可以自定义
- **`command`**: Python解释器路径，建议使用 `python3`
- **`args`**: 启动参数，指向服务器脚本
- **`cwd`**: 工作目录，需要指向项目的绝对路径
- **`env`**: 环境变量，设置Python路径

## 不同客户端配置方法

### 1. <PERSON>

#### 配置位置
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

#### 配置内容
```json
{
  "mcpServers": {
    "datasource-query": {
      "command": "python3",
      "args": ["/Users/<USER>/augment_project/remote_mcp_test/server.py"],
      "cwd": "/Users/<USER>/augment_project/remote_mcp_test"
    }
  }
}
```

#### 使用步骤
1. 关闭Claude Desktop
2. 编辑配置文件
3. 重启Claude Desktop
4. 在对话中使用数据源查询功能

### 2. 其他MCP客户端

#### 通用配置模板
```json
{
  "servers": {
    "datasource-query": {
      "command": "python3",
      "args": ["server.py"],
      "cwd": "/path/to/augment_project/remote_mcp_test",
      "env": {
        "PYTHONPATH": "/path/to/augment_project/remote_mcp_test"
      }
    }
  }
}
```

## 配置验证

### 1. 手动测试
```bash
cd /Users/<USER>/augment_project/remote_mcp_test
./start.sh test
```

### 2. 检查依赖
```bash
python3 -c "import aiohttp; print('aiohttp installed')"
```

### 3. 验证服务器启动
```bash
python3 server.py
# 应该等待输入，不报错
```

## 常见问题解决

### 问题1: Python命令不存在
**错误**: `command not found: python3`

**解决方案**:
```json
{
  "command": "/usr/bin/python3"  // 使用绝对路径
}
```

或者检查Python安装:
```bash
which python3
```

### 问题2: 模块导入错误
**错误**: `ModuleNotFoundError: No module named 'aiohttp'`

**解决方案**:
1. 安装依赖:
```bash
python3 -m pip install aiohttp
```

2. 或在配置中指定Python路径:
```json
{
  "env": {
    "PYTHONPATH": "/path/to/site-packages"
  }
}
```

### 问题3: 工作目录错误
**错误**: `FileNotFoundError: server.py`

**解决方案**:
确保 `cwd` 指向正确的项目目录:
```json
{
  "cwd": "/Users/<USER>/augment_project/remote_mcp_test"
}
```

### 问题4: 权限问题
**错误**: `Permission denied`

**解决方案**:
```bash
chmod +x server.py
chmod +x start.sh
```

## 高级配置

### 1. 环境变量配置
```json
{
  "env": {
    "PYTHONPATH": "/custom/path",
    "LOG_LEVEL": "DEBUG",
    "API_TIMEOUT": "30"
  }
}
```

### 2. 多服务器配置
```json
{
  "mcpServers": {
    "datasource-query": {
      "command": "python3",
      "args": ["server.py"],
      "cwd": "/path/to/datasource/server"
    },
    "other-service": {
      "command": "python3",
      "args": ["other_server.py"],
      "cwd": "/path/to/other/server"
    }
  }
}
```

### 3. 使用启动脚本
```json
{
  "mcpServers": {
    "datasource-query": {
      "command": "/Users/<USER>/augment_project/remote_mcp_test/start.sh",
      "args": ["mcp"],
      "cwd": "/Users/<USER>/augment_project/remote_mcp_test"
    }
  }
}
```

## 使用示例

### 在Claude Desktop中使用

1. 配置完成后，在Claude对话中可以这样使用：

```
请帮我查询包含"数据库"关键词的数据源信息
```

2. Claude会自动调用 `search_datasource_info` 工具

3. 你也可以直接请求：

```
使用数据源查询工具搜索"mysql"相关的数据源，返回前10条结果
```

### API参数说明

工具会接受以下参数：
- `query`: 搜索关键词（必需）
- `page`: 页码（可选，默认1）
- `size`: 每页结果数（可选，默认10）
- `additional_params`: 额外参数（可选）

## 故障排除

### 启用调试模式
在服务器代码中临时添加调试信息：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 查看日志
大多数MCP客户端会在控制台或日志文件中显示服务器输出。

### 测试连接
使用提供的测试客户端验证服务器功能：
```bash
python3 test_client.py
```

## 总结

正确配置MCP Server需要：
1. ✅ 正确的Python路径
2. ✅ 准确的工作目录
3. ✅ 必要的依赖包
4. ✅ 适当的文件权限

按照本指南配置后，你的MCP Server应该能够在支持MCP协议的客户端中正常工作。
