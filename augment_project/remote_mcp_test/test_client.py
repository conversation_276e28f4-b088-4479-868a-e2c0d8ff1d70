#!/usr/bin/env python3
"""
Test client for the datasource query MCP server
"""

import asyncio
import json
import subprocess
import sys

async def test_server():
    """Test the MCP server functionality."""

    # Start the server process
    process = await asyncio.create_subprocess_exec(
        "python3", "server.py",
        stdin=asyncio.subprocess.PIPE,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )

    try:
        # Test initialization
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }

        # Send initialization request
        process.stdin.write((json.dumps(init_request) + "\n").encode())
        await process.stdin.drain()

        # Read response
        response_line = await process.stdout.readline()
        init_response = json.loads(response_line.decode().strip())

        print("🚀 MCP Server initialized successfully!")
        print(f"📋 Server info: {init_response['result']['serverInfo']}")

        # Test tools/list
        list_tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list",
            "params": {}
        }

        process.stdin.write((json.dumps(list_tools_request) + "\n").encode())
        await process.stdin.drain()

        response_line = await process.stdout.readline()
        tools_response = json.loads(response_line.decode().strip())

        tools = tools_response['result']['tools']
        print(f"\n📋 Available tools: {len(tools)}")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")

        # Test tool call
        print("\n🔍 Testing search_datasource_info tool...")

        call_tool_request = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "search_datasource_info",
                "arguments": {
                    "query": "test",
                    "page": 1,
                    "size": 5
                }
            }
        }

        process.stdin.write((json.dumps(call_tool_request) + "\n").encode())
        await process.stdin.drain()

        response_line = await process.stdout.readline()
        call_response = json.loads(response_line.decode().strip())

        print("✅ Tool call successful!")
        print("📄 Response:")

        if 'result' in call_response:
            content = call_response['result']['content'][0]['text']
            try:
                response_data = json.loads(content)
                print(json.dumps(response_data, indent=2, ensure_ascii=False))
            except json.JSONDecodeError:
                print(content)
        else:
            print(f"Error: {call_response.get('error', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        # Read stderr for debugging
        stderr_output = await process.stderr.read()
        if stderr_output:
            print(f"Server stderr: {stderr_output.decode()}")
    finally:
        # Clean up
        process.terminate()
        await process.wait()

if __name__ == "__main__":
    print("🧪 Testing Datasource Query MCP Server")
    print("=" * 50)

    try:
        asyncio.run(test_server())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
