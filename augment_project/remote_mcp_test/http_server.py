#!/usr/bin/env python3
"""
HTTP version of the MCP Server for querying data source information
"""

import asyncio
import json
import logging
from typing import Any, Dict
import aiohttp
from aiohttp import web

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API endpoint
API_ENDPOINT = "https://pre-qanat.aliyun-inc.com/api/searchDsInfo"

class DataSourceQueryServer:
    """HTTP server for data source queries."""
    
    def __init__(self):
        self.app = web.Application()
        self.setup_routes()
    
    def setup_routes(self):
        """Setup HTTP routes."""
        self.app.router.add_post('/search', self.search_datasource)
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/', self.index)
    
    async def index(self, request):
        """Index page with API documentation."""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Data Source Query API</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .endpoint { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .method { color: #fff; padding: 3px 8px; border-radius: 3px; font-weight: bold; }
                .post { background: #49cc90; }
                .get { background: #61affe; }
                pre { background: #f8f8f8; padding: 10px; border-radius: 3px; overflow-x: auto; }
            </style>
        </head>
        <body>
            <h1>Data Source Query API</h1>
            <p>This API provides access to Aliyun data source information.</p>
            
            <div class="endpoint">
                <h3><span class="method post">POST</span> /search</h3>
                <p>Search for data source information</p>
                <h4>Request Body:</h4>
                <pre>{
  "query": "search term",
  "page": 1,
  "size": 10,
  "additional_params": {}
}</pre>
                <h4>Example Response:</h4>
                <pre>{
  "status": "success",
  "status_code": 200,
  "data": [...],
  "query_params": {...}
}</pre>
            </div>
            
            <div class="endpoint">
                <h3><span class="method get">GET</span> /health</h3>
                <p>Health check endpoint</p>
            </div>
            
            <h3>Example Usage:</h3>
            <pre>curl -X POST http://localhost:8080/search \\
  -H "Content-Type: application/json" \\
  -d '{"query": "database", "page": 1, "size": 5}'</pre>
        </body>
        </html>
        """
        return web.Response(text=html, content_type='text/html')
    
    async def health_check(self, request):
        """Health check endpoint."""
        return web.json_response({"status": "healthy", "service": "datasource-query-api"})
    
    async def search_datasource(self, request):
        """Search for data source information."""
        try:
            # Parse request body
            data = await request.json()
            
            # Extract parameters
            query = data.get("query", "")
            page = data.get("page", 1)
            size = data.get("size", 10)
            additional_params = data.get("additional_params", {})
            
            if not query:
                return web.json_response(
                    {"error": "Query parameter is required"}, 
                    status=400
                )
            
            # Prepare request parameters
            request_params = {
                "query": query,
                "page": page,
                "size": size,
                **additional_params
            }
            
            # Make API request
            async with aiohttp.ClientSession() as session:
                logger.info(f"Making API request to {API_ENDPOINT} with data: {request_params}")
                
                headers = {'Content-Type': 'application/json'}
                async with session.post(API_ENDPOINT, json=request_params, headers=headers) as response:
                    if response.status == 200:
                        # Try to parse as JSON first, fallback to text
                        try:
                            api_data = await response.json()
                        except:
                            # If JSON parsing fails, get as text
                            text_data = await response.text()
                            try:
                                # Try to parse the text as JSON
                                api_data = json.loads(text_data)
                            except:
                                # If still fails, return as text
                                api_data = text_data
                        
                        result = {
                            "status": "success",
                            "status_code": response.status,
                            "data": api_data,
                            "query_params": request_params
                        }
                    else:
                        error_text = await response.text()
                        result = {
                            "status": "error",
                            "status_code": response.status,
                            "error": error_text,
                            "query_params": request_params
                        }
                    
                    return web.json_response(result)
                    
        except aiohttp.ClientError as e:
            logger.error(f"Network error: {e}")
            return web.json_response({
                "status": "error",
                "error_type": "network_error",
                "error": str(e)
            }, status=502)
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            return web.json_response({
                "status": "error",
                "error_type": "json_error",
                "error": "Invalid JSON in request body"
            }, status=400)
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return web.json_response({
                "status": "error",
                "error_type": "unexpected_error",
                "error": str(e)
            }, status=500)

async def main():
    """Main entry point for the HTTP server."""
    server = DataSourceQueryServer()
    
    # Setup CORS if needed
    @web.middleware
    async def cors_handler(request, handler):
        response = await handler(request)
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
        return response

    server.app.middlewares.append(cors_handler)
    
    # Start the server
    runner = web.AppRunner(server.app)
    await runner.setup()
    
    site = web.TCPSite(runner, 'localhost', 8080)
    await site.start()
    
    print("🚀 HTTP Server started at http://localhost:8080")
    print("📋 Available endpoints:")
    print("  - GET  / : API documentation")
    print("  - POST /search : Search data sources")
    print("  - GET  /health : Health check")
    print("\n💡 Example usage:")
    print("curl -X POST http://localhost:8080/search -H 'Content-Type: application/json' -d '{\"query\": \"test\", \"page\": 1, \"size\": 5}'")
    
    # Keep the server running
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 Shutting down server...")
    finally:
        await runner.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
