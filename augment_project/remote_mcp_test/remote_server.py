#!/usr/bin/env python3
"""
Remote MCP Server using Server-Sent Events (SSE)
Clients can connect via HTTP and communicate using MCP protocol
"""

import asyncio
import json
import logging
import uuid
from typing import Any, Dict, List, Optional
import aiohttp
from aiohttp import web, WSMsgType
import weakref
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API endpoint
API_ENDPOINT = "https://pre-qanat.aliyun-inc.com/api/searchDsInfo"

class MCPSession:
    """Represents a single MCP client session."""
    
    def __init__(self, session_id: str, response: web.StreamResponse):
        self.session_id = session_id
        self.response = response
        self.initialized = False
        self.created_at = time.time()
        
    async def send_message(self, message: Dict[str, Any]):
        """Send a message to the client via SSE."""
        try:
            data = json.dumps(message)
            await self.response.write(f"data: {data}\n\n".encode())
        except Exception as e:
            logger.error(f"Error sending message to {self.session_id}: {e}")
            raise

class RemoteMCPServer:
    """Remote MCP Server with SSE transport."""
    
    def __init__(self):
        self.app = web.Application()
        self.sessions: Dict[str, MCPSession] = {}
        self.setup_routes()
        
    def setup_routes(self):
        """Setup HTTP routes."""
        # MCP SSE endpoint
        self.app.router.add_get('/mcp', self.handle_sse_connection)
        self.app.router.add_post('/mcp', self.handle_mcp_request)
        
        # WebSocket alternative
        self.app.router.add_get('/ws', self.handle_websocket)
        
        # Management endpoints
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/sessions', self.list_sessions)
        self.app.router.add_get('/', self.index)
        
        # CORS middleware
        self.app.middlewares.append(self.cors_middleware)
    
    @web.middleware
    async def cors_middleware(self, request, handler):
        """CORS middleware."""
        response = await handler(request)
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, X-Session-ID'
        return response
    
    async def index(self, request):
        """Index page with documentation."""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Remote MCP Server</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .endpoint { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .method { color: #fff; padding: 3px 8px; border-radius: 3px; font-weight: bold; }
                .get { background: #61affe; }
                .post { background: #49cc90; }
                pre { background: #f8f8f8; padding: 10px; border-radius: 3px; overflow-x: auto; }
                .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
                .success { background: #d4edda; color: #155724; }
            </style>
        </head>
        <body>
            <h1>🌐 Remote MCP Server</h1>
            <div class="status success">
                <strong>Status:</strong> Server is running and ready to accept connections
            </div>
            
            <h2>Connection Methods</h2>
            
            <div class="endpoint">
                <h3><span class="method get">GET</span> /mcp</h3>
                <p><strong>SSE Connection</strong> - Establish Server-Sent Events connection for MCP communication</p>
                <p>This endpoint provides a persistent connection for receiving MCP responses.</p>
            </div>
            
            <div class="endpoint">
                <h3><span class="method post">POST</span> /mcp</h3>
                <p><strong>MCP Request</strong> - Send MCP protocol messages</p>
                <p>Headers: <code>X-Session-ID: your-session-id</code></p>
                <h4>Example Request:</h4>
                <pre>{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "search_datasource_info",
    "arguments": {
      "query": "database",
      "page": 1,
      "size": 5
    }
  }
}</pre>
            </div>
            
            <div class="endpoint">
                <h3><span class="method get">GET</span> /ws</h3>
                <p><strong>WebSocket Connection</strong> - Alternative real-time communication</p>
                <p>Full-duplex communication for MCP protocol.</p>
            </div>
            
            <h2>Management Endpoints</h2>
            
            <div class="endpoint">
                <h3><span class="method get">GET</span> /health</h3>
                <p>Health check endpoint</p>
            </div>
            
            <div class="endpoint">
                <h3><span class="method get">GET</span> /sessions</h3>
                <p>List active sessions</p>
            </div>
            
            <h2>Client Configuration</h2>
            <p>To connect to this remote MCP server, configure your MCP client with:</p>
            <pre>URL: http://localhost:8080/mcp
Transport: SSE (Server-Sent Events)</pre>
            
            <h2>Example Client Code</h2>
            <pre>// JavaScript example
const sessionId = 'session-' + Date.now();
const eventSource = new EventSource('/mcp');
eventSource.onmessage = (event) => {
    const response = JSON.parse(event.data);
    console.log('Received:', response);
};

// Send request
fetch('/mcp', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': sessionId
    },
    body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {}
    })
});</pre>
        </body>
        </html>
        """
        return web.Response(text=html, content_type='text/html')
    
    async def health_check(self, request):
        """Health check endpoint."""
        return web.json_response({
            "status": "healthy",
            "service": "remote-mcp-server",
            "active_sessions": len(self.sessions),
            "uptime": time.time()
        })
    
    async def list_sessions(self, request):
        """List active sessions."""
        sessions_info = []
        for session_id, session in self.sessions.items():
            sessions_info.append({
                "session_id": session_id,
                "initialized": session.initialized,
                "created_at": session.created_at,
                "age_seconds": time.time() - session.created_at
            })
        
        return web.json_response({
            "active_sessions": len(self.sessions),
            "sessions": sessions_info
        })
    
    async def handle_sse_connection(self, request):
        """Handle SSE connection for MCP communication."""
        session_id = request.query.get('session_id', str(uuid.uuid4()))
        
        # Setup SSE response
        response = web.StreamResponse(
            status=200,
            reason='OK',
            headers={
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
            }
        )
        
        await response.prepare(request)
        
        # Create session
        session = MCPSession(session_id, response)
        self.sessions[session_id] = session
        
        logger.info(f"New SSE connection: {session_id}")
        
        try:
            # Send welcome message
            await session.send_message({
                "type": "connection",
                "session_id": session_id,
                "message": "Connected to Remote MCP Server"
            })
            
            # Keep connection alive
            while True:
                await asyncio.sleep(30)  # Send keepalive every 30 seconds
                await session.send_message({
                    "type": "keepalive",
                    "timestamp": time.time()
                })
                
        except Exception as e:
            logger.error(f"SSE connection error for {session_id}: {e}")
        finally:
            # Cleanup session
            if session_id in self.sessions:
                del self.sessions[session_id]
            logger.info(f"SSE connection closed: {session_id}")
        
        return response

    async def handle_mcp_request(self, request):
        """Handle MCP protocol requests via POST."""
        session_id = request.headers.get('X-Session-ID')
        if not session_id:
            return web.json_response(
                {"error": "Missing X-Session-ID header"},
                status=400
            )

        try:
            # Parse request
            data = await request.json()

            # Process MCP request
            response = await self.process_mcp_request(session_id, data)

            # Send response via SSE if session exists
            if session_id in self.sessions:
                session = self.sessions[session_id]
                await session.send_message(response)

            # Also return via HTTP response
            return web.json_response(response)

        except Exception as e:
            logger.error(f"Error processing MCP request: {e}")
            error_response = {
                "jsonrpc": "2.0",
                "id": data.get("id") if 'data' in locals() else None,
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }
            return web.json_response(error_response, status=500)

    async def handle_websocket(self, request):
        """Handle WebSocket connection for MCP communication."""
        ws = web.WebSocketResponse()
        await ws.prepare(request)

        session_id = str(uuid.uuid4())
        logger.info(f"New WebSocket connection: {session_id}")

        try:
            # Send welcome message
            await ws.send_str(json.dumps({
                "type": "connection",
                "session_id": session_id,
                "message": "Connected to Remote MCP Server via WebSocket"
            }))

            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        response = await self.process_mcp_request(session_id, data)
                        await ws.send_str(json.dumps(response))
                    except Exception as e:
                        error_response = {
                            "jsonrpc": "2.0",
                            "error": {
                                "code": -32603,
                                "message": f"Error: {str(e)}"
                            }
                        }
                        await ws.send_str(json.dumps(error_response))
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f"WebSocket error: {ws.exception()}")
                    break

        except Exception as e:
            logger.error(f"WebSocket connection error: {e}")
        finally:
            logger.info(f"WebSocket connection closed: {session_id}")

        return ws

    async def process_mcp_request(self, session_id: str, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process MCP protocol requests."""
        method = request.get("method")
        params = request.get("params", {})
        request_id = request.get("id")

        try:
            if method == "initialize":
                return await self.handle_initialize(session_id, request_id, params)
            elif method == "tools/list":
                return await self.handle_list_tools(request_id)
            elif method == "tools/call":
                return await self.handle_call_tool(request_id, params)
            else:
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32601,
                        "message": f"Method not found: {method}"
                    }
                }
        except Exception as e:
            logger.error(f"Error processing MCP request: {e}")
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }

    async def handle_initialize(self, session_id: str, request_id: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle initialization request."""
        if session_id in self.sessions:
            self.sessions[session_id].initialized = True

        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "serverInfo": {
                    "name": "remote-datasource-query-server",
                    "version": "1.0.0"
                }
            }
        }

    async def handle_list_tools(self, request_id: int) -> Dict[str, Any]:
        """Handle tools/list request."""
        tools = [
            {
                "name": "search_datasource_info",
                "description": "Search for data source information using the Aliyun API (Remote)",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query for data source information"
                        },
                        "page": {
                            "type": "integer",
                            "description": "Page number for pagination (optional)",
                            "default": 1
                        },
                        "size": {
                            "type": "integer",
                            "description": "Number of results per page (optional)",
                            "default": 10
                        },
                        "additional_params": {
                            "type": "object",
                            "description": "Additional query parameters (optional)",
                            "additionalProperties": True
                        }
                    },
                    "required": ["query"]
                }
            }
        ]

        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "tools": tools
            }
        }

    async def handle_call_tool(self, request_id: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle tools/call request."""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})

        if tool_name != "search_datasource_info":
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32602,
                    "message": f"Unknown tool: {tool_name}"
                }
            }

        try:
            # Extract parameters
            query = arguments.get("query", "")
            page = arguments.get("page", 1)
            size = arguments.get("size", 10)
            additional_params = arguments.get("additional_params", {})

            # Prepare request parameters
            request_params = {
                "query": query,
                "page": page,
                "size": size,
                **additional_params
            }

            # Make API request
            async with aiohttp.ClientSession() as session:
                logger.info(f"Remote MCP: Making API request to {API_ENDPOINT} with data: {request_params}")

                headers = {'Content-Type': 'application/json'}
                async with session.post(API_ENDPOINT, json=request_params, headers=headers) as response:
                    if response.status == 200:
                        # Try to parse as JSON first, fallback to text
                        try:
                            data = await response.json()
                        except:
                            # If JSON parsing fails, get as text
                            text_data = await response.text()
                            try:
                                # Try to parse the text as JSON
                                data = json.loads(text_data)
                            except:
                                # If still fails, return as text
                                data = text_data

                        result = {
                            "status": "success",
                            "status_code": response.status,
                            "data": data,
                            "query_params": request_params,
                            "server_type": "remote_mcp"
                        }
                    else:
                        error_text = await response.text()
                        result = {
                            "status": "error",
                            "status_code": response.status,
                            "error": error_text,
                            "query_params": request_params,
                            "server_type": "remote_mcp"
                        }

                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "content": [
                                {
                                    "type": "text",
                                    "text": json.dumps(result, indent=2, ensure_ascii=False)
                                }
                            ]
                        }
                    }

        except Exception as e:
            error_result = {
                "status": "error",
                "error_type": "unexpected_error",
                "error": str(e),
                "query_params": request_params if 'request_params' in locals() else arguments,
                "server_type": "remote_mcp"
            }
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(error_result, indent=2, ensure_ascii=False)
                        }
                    ]
                }
            }

async def main():
    """Main entry point for the remote server."""
    server = RemoteMCPServer()

    # Start the server
    runner = web.AppRunner(server.app)
    await runner.setup()

    site = web.TCPSite(runner, 'localhost', 8080)
    await site.start()

    print("🌐 Remote MCP Server started at http://localhost:8080")
    print("📋 Available endpoints:")
    print("  - GET  / : Server documentation")
    print("  - GET  /mcp : SSE connection for MCP")
    print("  - POST /mcp : Send MCP requests")
    print("  - GET  /ws : WebSocket connection")
    print("  - GET  /health : Health check")
    print("  - GET  /sessions : Active sessions")
    print("\n🔗 Client connection URL: http://localhost:8080/mcp")
    print("🔗 WebSocket URL: ws://localhost:8080/ws")

    # Keep the server running
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 Shutting down remote server...")
    finally:
        await runner.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
