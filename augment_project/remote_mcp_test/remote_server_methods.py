#!/usr/bin/env python3
"""
Additional methods for Remote MCP Server
This file contains the remaining methods that will be integrated into remote_server.py
"""

import json
import logging
import aiohttp
from typing import Any, Dict

logger = logging.getLogger(__name__)
API_ENDPOINT = "https://pre-qanat.aliyun-inc.com/api/searchDsInfo"

async def process_mcp_request(self, session_id: str, request: Dict[str, Any]) -> Dict[str, Any]:
    """Process MCP protocol requests."""
    method = request.get("method")
    params = request.get("params", {})
    request_id = request.get("id")
    
    try:
        if method == "initialize":
            return await self.handle_initialize(session_id, request_id, params)
        elif method == "tools/list":
            return await self.handle_list_tools(request_id)
        elif method == "tools/call":
            return await self.handle_call_tool(request_id, params)
        else:
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32601,
                    "message": f"Method not found: {method}"
                }
            }
    except Exception as e:
        logger.error(f"Error processing MCP request: {e}")
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": -32603,
                "message": f"Internal error: {str(e)}"
            }
        }

async def handle_initialize(self, session_id: str, request_id: int, params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle initialization request."""
    if session_id in self.sessions:
        self.sessions[session_id].initialized = True
    
    return {
        "jsonrpc": "2.0",
        "id": request_id,
        "result": {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "serverInfo": {
                "name": "remote-datasource-query-server",
                "version": "1.0.0"
            }
        }
    }

async def handle_list_tools(self, request_id: int) -> Dict[str, Any]:
    """Handle tools/list request."""
    tools = [
        {
            "name": "search_datasource_info",
            "description": "Search for data source information using the Aliyun API (Remote)",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query for data source information"
                    },
                    "page": {
                        "type": "integer",
                        "description": "Page number for pagination (optional)",
                        "default": 1
                    },
                    "size": {
                        "type": "integer", 
                        "description": "Number of results per page (optional)",
                        "default": 10
                    },
                    "additional_params": {
                        "type": "object",
                        "description": "Additional query parameters (optional)",
                        "additionalProperties": True
                    }
                },
                "required": ["query"]
            }
        }
    ]
    
    return {
        "jsonrpc": "2.0",
        "id": request_id,
        "result": {
            "tools": tools
        }
    }

async def handle_call_tool(self, request_id: int, params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle tools/call request."""
    tool_name = params.get("name")
    arguments = params.get("arguments", {})
    
    if tool_name != "search_datasource_info":
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": -32602,
                "message": f"Unknown tool: {tool_name}"
            }
        }
    
    try:
        # Extract parameters
        query = arguments.get("query", "")
        page = arguments.get("page", 1)
        size = arguments.get("size", 10)
        additional_params = arguments.get("additional_params", {})
        
        # Prepare request parameters
        request_params = {
            "query": query,
            "page": page,
            "size": size,
            **additional_params
        }
        
        # Make API request
        async with aiohttp.ClientSession() as session:
            logger.info(f"Remote MCP: Making API request to {API_ENDPOINT} with data: {request_params}")
            
            headers = {'Content-Type': 'application/json'}
            async with session.post(API_ENDPOINT, json=request_params, headers=headers) as response:
                if response.status == 200:
                    # Try to parse as JSON first, fallback to text
                    try:
                        data = await response.json()
                    except:
                        # If JSON parsing fails, get as text
                        text_data = await response.text()
                        try:
                            # Try to parse the text as JSON
                            data = json.loads(text_data)
                        except:
                            # If still fails, return as text
                            data = text_data
                    
                    result = {
                        "status": "success",
                        "status_code": response.status,
                        "data": data,
                        "query_params": request_params,
                        "server_type": "remote_mcp"
                    }
                else:
                    error_text = await response.text()
                    result = {
                        "status": "error",
                        "status_code": response.status,
                        "error": error_text,
                        "query_params": request_params,
                        "server_type": "remote_mcp"
                    }
                
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": json.dumps(result, indent=2, ensure_ascii=False)
                            }
                        ]
                    }
                }
                
    except Exception as e:
        error_result = {
            "status": "error",
            "error_type": "unexpected_error",
            "error": str(e),
            "query_params": request_params if 'request_params' in locals() else arguments,
            "server_type": "remote_mcp"
        }
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "content": [
                    {
                        "type": "text",
                        "text": json.dumps(error_result, indent=2, ensure_ascii=False)
                    }
                ]
            }
        }

# Main function for the remote server
async def main():
    """Main entry point for the remote server."""
    from aiohttp import web
    import asyncio
    
    # Import the server class
    from remote_server import RemoteMCPServer
    
    server = RemoteMCPServer()
    
    # Add the methods to the server instance
    server.process_mcp_request = process_mcp_request.__get__(server, RemoteMCPServer)
    server.handle_initialize = handle_initialize.__get__(server, RemoteMCPServer)
    server.handle_list_tools = handle_list_tools.__get__(server, RemoteMCPServer)
    server.handle_call_tool = handle_call_tool.__get__(server, RemoteMCPServer)
    
    # Start the server
    runner = web.AppRunner(server.app)
    await runner.setup()
    
    site = web.TCPSite(runner, 'localhost', 8080)
    await site.start()
    
    print("🌐 Remote MCP Server started at http://localhost:8080")
    print("📋 Available endpoints:")
    print("  - GET  / : Server documentation")
    print("  - GET  /mcp : SSE connection for MCP")
    print("  - POST /mcp : Send MCP requests")
    print("  - GET  /ws : WebSocket connection")
    print("  - GET  /health : Health check")
    print("  - GET  /sessions : Active sessions")
    print("\n🔗 Client connection URL: http://localhost:8080/mcp")
    print("🔗 WebSocket URL: ws://localhost:8080/ws")
    
    # Keep the server running
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 Shutting down remote server...")
    finally:
        await runner.cleanup()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
