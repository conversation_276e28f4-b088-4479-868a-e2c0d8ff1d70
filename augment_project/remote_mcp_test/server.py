#!/usr/bin/env python3
"""
Remote MCP Server for querying data source information
"""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional, Union
import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API endpoint
API_ENDPOINT = "https://pre-qanat.aliyun-inc.com/api/searchDsInfo"

class MCPServer:
    """Simple MCP Server implementation for data source queries."""

    def __init__(self, name: str):
        self.name = name
        self.request_id = 0

    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle incoming MCP requests."""
        method = request.get("method")
        params = request.get("params", {})
        request_id = request.get("id")

        try:
            if method == "initialize":
                return await self.handle_initialize(request_id, params)
            elif method == "tools/list":
                return await self.handle_list_tools(request_id)
            elif method == "tools/call":
                return await self.handle_call_tool(request_id, params)
            else:
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32601,
                        "message": f"Method not found: {method}"
                    }
                }
        except Exception as e:
            logger.error(f"Error handling request: {e}")
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }

    async def handle_initialize(self, request_id: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle initialization request."""
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "serverInfo": {
                    "name": self.name,
                    "version": "1.0.0"
                }
            }
        }

    async def handle_list_tools(self, request_id: int) -> Dict[str, Any]:
        """Handle tools/list request."""
        tools = [
            {
                "name": "search_datasource_info",
                "description": "Search for data source information using the Aliyun API",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query for data source information"
                        },
                        "page": {
                            "type": "integer",
                            "description": "Page number for pagination (optional)",
                            "default": 1
                        },
                        "size": {
                            "type": "integer",
                            "description": "Number of results per page (optional)",
                            "default": 10
                        },
                        "additional_params": {
                            "type": "object",
                            "description": "Additional query parameters (optional)",
                            "additionalProperties": True
                        }
                    },
                    "required": ["query"]
                }
            }
        ]

        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "tools": tools
            }
        }

    async def handle_call_tool(self, request_id: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle tools/call request."""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})

        if tool_name != "search_datasource_info":
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32602,
                    "message": f"Unknown tool: {tool_name}"
                }
            }

        try:
            # Extract parameters
            query = arguments.get("query", "")
            page = arguments.get("page", 1)
            size = arguments.get("size", 10)
            additional_params = arguments.get("additional_params", {})

            # Prepare request parameters
            request_params = {
                "query": query,
                "page": page,
                "size": size,
                **additional_params
            }

            # Make API request
            async with aiohttp.ClientSession() as session:
                logger.info(f"Making API request to {API_ENDPOINT} with data: {request_params}")

                # Try POST request with JSON body first
                headers = {'Content-Type': 'application/json'}
                async with session.post(API_ENDPOINT, json=request_params, headers=headers) as response:
                    if response.status == 200:
                        # Try to parse as JSON first, fallback to text
                        try:
                            data = await response.json()
                        except:
                            # If JSON parsing fails, get as text
                            text_data = await response.text()
                            try:
                                # Try to parse the text as JSON
                                data = json.loads(text_data)
                            except:
                                # If still fails, return as text
                                data = text_data

                        result = {
                            "status": "success",
                            "status_code": response.status,
                            "data": data,
                            "query_params": request_params
                        }
                    else:
                        error_text = await response.text()
                        result = {
                            "status": "error",
                            "status_code": response.status,
                            "error": error_text,
                            "query_params": request_params
                        }

                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "content": [
                                {
                                    "type": "text",
                                    "text": json.dumps(result, indent=2, ensure_ascii=False)
                                }
                            ]
                        }
                    }

        except aiohttp.ClientError as e:
            error_result = {
                "status": "error",
                "error_type": "network_error",
                "error": str(e),
                "query_params": request_params if 'request_params' in locals() else arguments
            }
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(error_result, indent=2, ensure_ascii=False)
                        }
                    ]
                }
            }
        except Exception as e:
            error_result = {
                "status": "error",
                "error_type": "unexpected_error",
                "error": str(e),
                "query_params": request_params if 'request_params' in locals() else arguments
            }
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(error_result, indent=2, ensure_ascii=False)
                        }
                    ]
                }
            }

async def main():
    """Main entry point for the server."""
    server = MCPServer("datasource-query-server")

    # Read from stdin and write to stdout
    while True:
        try:
            line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
            if not line:
                break

            line = line.strip()
            if not line:
                continue

            # Parse JSON-RPC request
            try:
                request = json.loads(line)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON: {e}")
                continue

            # Handle request
            response = await server.handle_request(request)

            # Send response
            print(json.dumps(response), flush=True)

        except KeyboardInterrupt:
            break
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
            break

if __name__ == "__main__":
    asyncio.run(main())
