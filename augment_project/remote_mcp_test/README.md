# Datasource Query MCP Server

这是一个MCP (Model Context Protocol) server，用于调用阿里云API查询数据源信息。

## 功能特性

- 🔍 查询数据源信息
- 📄 支持分页查询
- 🛠️ 灵活的参数配置
- 🚀 异步处理
- 📋 详细的错误处理
- 🌐 提供HTTP API接口
- 🔧 兼容Python 3.9+

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. MCP Server模式

#### 启动MCP Server
```bash
python3 server.py
```

#### 测试MCP Server功能
```bash
python3 test_client.py
```

#### 在MCP客户端中使用
将以下配置添加到你的MCP客户端配置中：

```json
{
  "mcpServers": {
    "datasource-query": {
      "command": "python3",
      "args": ["server.py"],
      "cwd": "/path/to/this/directory",
      "env": {}
    }
  }
}
```

### 2. HTTP Server模式

#### 启动HTTP Server
```bash
python3 http_server.py
```

服务器将在 http://localhost:8080 启动

#### 使用HTTP API

**查询数据源信息：**
```bash
curl -X POST http://localhost:8080/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "数据库",
    "page": 1,
    "size": 10
  }'
```

**健康检查：**
```bash
curl http://localhost:8080/health
```

**查看API文档：**
访问 http://localhost:8080 查看完整的API文档

## 可用工具

### search_datasource_info

查询数据源信息的工具。

**参数：**
- `query` (必需): 搜索查询字符串
- `page` (可选): 页码，默认为1
- `size` (可选): 每页结果数量，默认为10
- `additional_params` (可选): 额外的查询参数

**示例：**
```json
{
  "query": "数据库",
  "page": 1,
  "size": 20,
  "additional_params": {
    "type": "mysql"
  }
}
```

## API端点

- **URL**: `https://pre-qanat.aliyun-inc.com/api/searchDsInfo`
- **方法**: GET
- **参数**: 通过URL查询参数传递

## 响应格式

成功响应：
```json
{
  "status": "success",
  "status_code": 200,
  "data": {
    // API返回的数据
  },
  "query_params": {
    // 发送的查询参数
  }
}
```

错误响应：
```json
{
  "status": "error",
  "status_code": 400,
  "error": "错误信息",
  "query_params": {
    // 发送的查询参数
  }
}
```

## 开发说明

- 使用Python 3.7+
- 基于aiohttp进行异步HTTP请求
- 遵循MCP协议规范
- 支持stdio传输方式

## 故障排除

1. **依赖安装问题**：确保安装了所有必需的依赖包
2. **网络连接问题**：检查是否能访问API端点
3. **参数错误**：确保传递的参数符合API要求

## 许可证

MIT License
