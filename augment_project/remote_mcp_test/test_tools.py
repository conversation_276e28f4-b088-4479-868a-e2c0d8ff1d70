#!/usr/bin/env python3
"""
Simple test script to check tools list
"""

import asyncio
import json
import aiohttp
import uuid

async def test_tools_list():
    """Test tools list endpoint."""
    session_id = str(uuid.uuid4())
    
    # Test tools/list request
    request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/list",
        "params": {}
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-Session-ID": session_id
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            # Send request
            async with session.post(
                "http://localhost:8080/mcp",
                json=request,
                headers=headers
            ) as response:
                print(f"Status: {response.status}")
                print(f"Headers: {dict(response.headers)}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"Response: {json.dumps(data, indent=2)}")
                    
                    # Extract tools
                    if 'result' in data and 'tools' in data['result']:
                        tools = data['result']['tools']
                        print(f"\n✅ Found {len(tools)} tools:")
                        for tool in tools:
                            print(f"  - {tool['name']}: {tool['description']}")
                    else:
                        print("❌ No tools found in response")
                else:
                    error_text = await response.text()
                    print(f"❌ Error: {error_text}")
                    
        except Exception as e:
            print(f"❌ Connection error: {e}")

if __name__ == "__main__":
    print("🧪 Testing Tools List")
    print("=" * 30)
    asyncio.run(test_tools_list())
